<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug WebSocket</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1e1e1e;
            color: #f0f0f0;
            font-family: monospace;
        }
        .log {
            background: #333;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .error { background: #660000; }
        .success { background: #006600; }
        .info { background: #000066; }
    </style>
</head>
<body>
    <h1>WebSocket Debug</h1>
    <button onclick="testConnection()">Test WebSocket Connection</button>
    <button onclick="clearLogs()">Clear Logs</button>
    <div id="logs"></div>

    <script>
        const logsDiv = document.getElementById('logs');
        let socket = null;

        function log(message, type = 'info') {
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            logsDiv.innerHTML = '';
        }

        function testConnection() {
            if (socket) {
                log('Closing existing connection...', 'info');
                socket.close();
                socket = null;
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const backendHost = window.location.hostname + ':8081';
            const wsUrl = `${protocol}//${backendHost}/ws`;
            
            log(`Attempting to connect to: ${wsUrl}`, 'info');
            log(`Current location: ${window.location.href}`, 'info');
            log(`Protocol: ${protocol}`, 'info');
            log(`Backend host: ${backendHost}`, 'info');

            try {
                socket = new WebSocket(wsUrl);

                socket.onopen = function(event) {
                    log('WebSocket connection opened successfully!', 'success');
                    log(`ReadyState: ${socket.readyState}`, 'info');
                    
                    // Send a test message
                    const testMessage = JSON.stringify({
                        type: 'input',
                        data: 'echo "Hello from browser"\n'
                    });
                    socket.send(testMessage);
                    log(`Sent test message: ${testMessage}`, 'info');
                };

                socket.onmessage = function(event) {
                    log(`Received message: ${event.data}`, 'success');
                    try {
                        const parsed = JSON.parse(event.data);
                        log(`Parsed message: ${JSON.stringify(parsed, null, 2)}`, 'info');
                    } catch (e) {
                        log(`Raw message (not JSON): ${event.data}`, 'info');
                    }
                };

                socket.onclose = function(event) {
                    log(`WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}, WasClean: ${event.wasClean}`, 'error');
                    socket = null;
                };

                socket.onerror = function(error) {
                    log(`WebSocket error: ${error}`, 'error');
                    log(`Error object: ${JSON.stringify(error)}`, 'error');
                };

                // Log connection state after a short delay
                setTimeout(() => {
                    if (socket) {
                        log(`Connection state after 1s: ${socket.readyState}`, 'info');
                        const states = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
                        log(`State name: ${states[socket.readyState]}`, 'info');
                    }
                }, 1000);

            } catch (error) {
                log(`Exception creating WebSocket: ${error}`, 'error');
                log(`Error stack: ${error.stack}`, 'error');
            }
        }

        // Test connection on page load
        window.addEventListener('load', () => {
            log('Page loaded, testing connection...', 'info');
            testConnection();
        });
    </script>
</body>
</html>
