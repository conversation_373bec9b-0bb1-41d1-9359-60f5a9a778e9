<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Terminal Test</title>
    <link rel="stylesheet" href="https://unpkg.com/@xterm/xterm@5.3.0/css/xterm.css" />
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1e1e1e;
            color: #f0f0f0;
            font-family: monospace;
        }
        #terminal {
            width: 100%;
            height: 500px;
            background: #000;
        }
        #status {
            margin-bottom: 10px;
            padding: 10px;
            background: #333;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="status">Loading...</div>
    <div id="terminal"></div>

    <script src="https://unpkg.com/@xterm/xterm@5.3.0/lib/xterm.js"></script>
    <script src="https://unpkg.com/@xterm/addon-fit@0.8.0/lib/addon-fit.js"></script>
    <script>
        const statusDiv = document.getElementById('status');
        const terminalDiv = document.getElementById('terminal');

        // Create terminal
        const terminal = new Terminal({
            fontFamily: 'Menlo, Monaco, "Courier New", monospace',
            fontSize: 14,
            lineHeight: 1.2,
            cursorBlink: true,
            theme: {
                background: '#000000',
                foreground: '#f0f0f0',
                cursor: '#f0f0f0',
            }
        });

        // Create fit addon
        const fitAddon = new FitAddon.FitAddon();
        terminal.loadAddon(fitAddon);

        // Open terminal
        terminal.open(terminalDiv);
        fitAddon.fit();

        statusDiv.textContent = 'Terminal created, connecting to WebSocket...';

        // Connect to WebSocket
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const backendHost = window.location.hostname + ':8081';
        const wsUrl = `${protocol}//${backendHost}/ws`;
        
        console.log('Connecting to:', wsUrl);
        const socket = new WebSocket(wsUrl);

        socket.onopen = function(event) {
            statusDiv.textContent = 'Connected to WebSocket and Terminal';
            statusDiv.style.color = 'green';
            console.log('WebSocket connected');
            
            terminal.writeln('Connected to terminal server');
            terminal.write('$ ');
        };

        socket.onmessage = function(event) {
            try {
                const message = JSON.parse(event.data);
                if (message.type === 'output') {
                    terminal.write(message.data);
                }
            } catch (error) {
                console.error('Failed to parse message:', error);
                terminal.write(event.data);
            }
        };

        socket.onclose = function(event) {
            statusDiv.textContent = 'WebSocket connection closed';
            statusDiv.style.color = 'red';
            console.log('WebSocket closed');
            terminal.writeln('\r\nDisconnected from terminal server');
        };

        socket.onerror = function(error) {
            statusDiv.textContent = 'WebSocket error';
            statusDiv.style.color = 'red';
            console.error('WebSocket error:', error);
        };

        // Handle terminal input
        terminal.onData((data) => {
            console.log('Terminal input:', data);
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'input',
                    data: data
                }));
            } else {
                // Echo locally if not connected
                terminal.write(data);
            }
        });

        // Handle terminal resize
        terminal.onResize(({ cols, rows }) => {
            console.log('Terminal resized:', { cols, rows });
            if (socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'resize',
                    data: JSON.stringify({ cols, rows })
                }));
            }
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            fitAddon.fit();
        });
    </script>
</body>
</html>
