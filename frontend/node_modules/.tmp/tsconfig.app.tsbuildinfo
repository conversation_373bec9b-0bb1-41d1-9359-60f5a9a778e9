{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/aidialog/aidialog.tsx", "../../src/components/aidialog/messagelist.tsx", "../../src/components/aidialog/index.ts", "../../src/components/contentmodal/contentmodal.tsx", "../../src/components/contentmodal/index.ts", "../../src/components/errorboundary/errorboundary.tsx", "../../src/components/errorboundary/index.ts", "../../src/components/layout/footer.tsx", "../../src/components/layout/header.tsx", "../../src/components/layout/layout.tsx", "../../src/components/layout/index.ts", "../../src/components/settingsmodal/settingsmodal.tsx", "../../src/components/settingsmodal/index.ts", "../../src/components/statusbar/statusbar.tsx", "../../src/components/statusbar/index.ts", "../../src/components/terminal/terminal.tsx", "../../src/components/terminal/index.ts", "../../src/components/virtualkeyboard/virtualkeyboard.tsx", "../../src/components/virtualkeyboard/index.ts", "../../src/constants/index.ts", "../../src/services/aiservice.ts", "../../src/services/websocketmanager.ts", "../../src/services/xtermservice.ts", "../../src/stores/aistore.ts", "../../src/stores/keyboardstore.ts", "../../src/stores/terminalstore.ts", "../../src/stores/websocketstore.ts", "../../src/types/index.ts"], "version": "5.8.3"}