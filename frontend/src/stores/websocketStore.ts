import { create } from 'zustand';
import type { WebSocketState } from '@/types';
import { WEBSOCKET_CONFIG, API_ENDPOINTS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@/constants';

interface WebSocketStore extends WebSocketState {
  socket: WebSocket | null;
  reconnectTimer: number | null;
  
  // Actions
  connect: () => void;
  disconnect: () => void;
  send: (data: string) => boolean;
  setConnected: (connected: boolean) => void;
  setConnecting: (connecting: boolean) => void;
  setError: (error: string | null) => void;
  incrementReconnectAttempts: () => void;
  resetReconnectAttempts: () => void;
  scheduleReconnect: () => void;
}

export const useWebSocketStore = create<WebSocketStore>((set, get) => ({
  // Initial state
  socket: null,
  isConnected: false,
  isConnecting: false,
  error: null,
  reconnectAttempts: 0,
  reconnectTimer: null,

  // Actions
  connect: () => {
    debugger
    const state = get();
    
    if (state.socket && (state.socket.readyState === WebSocket.CONNECTING || state.socket.readyState === WebSocket.OPEN)) {
      console.log('Already connected or connecting');
      return;
    }

    set({ isConnecting: true, error: null });

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}${API_ENDPOINTS.WEBSOCKET}`;
      
      const socket = new WebSocket(wsUrl);

      socket.onopen = () => {
        console.log(SUCCESS_MESSAGES.WEBSOCKET_CONNECTED);
        set({
          isConnected: true,
          isConnecting: false,
          error: null,
          reconnectAttempts: 0,
        });
        
        // Clear reconnect timer if exists
        const currentState = get();
        if (currentState.reconnectTimer) {
          window.clearTimeout(currentState.reconnectTimer);
          set({ reconnectTimer: null });
        }
      };

      socket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          // Handle different message types here
          console.log('Received message:', message);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      socket.onclose = (event) => {
        console.log('WebSocket connection closed:', event.code, event.reason);
        set({
          isConnected: false,
          isConnecting: false,
        });

        // Attempt to reconnect if not manually closed
        if (event.code !== 1000) {
          get().scheduleReconnect();
        }
      };

      socket.onerror = (event) => {
        console.error('WebSocket error:', event);
        set({
          error: ERROR_MESSAGES.WEBSOCKET_CONNECTION_FAILED,
          isConnecting: false,
        });
      };

      set({ socket });

      // Return a cleanup function for this specific connection attempt
      return () => {
        if (socket && socket.readyState !== WebSocket.CLOSED) {
          console.log('Cleaning up WebSocket connection attempt...');
          socket.close(1000, 'Component unmounted during connection');
        }
      };
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      set({
        error: ERROR_MESSAGES.WEBSOCKET_CONNECTION_FAILED,
        isConnecting: false,
      });
    }
  },

  disconnect: () => {
    const state = get();
    
    // Clear reconnect timer
    if (state.reconnectTimer) {
      window.clearTimeout(state.reconnectTimer);
      set({ reconnectTimer: null });
    }

    const { socket } = get();
    if (socket) {
      // Remove listeners to prevent scheduleReconnect from firing on manual disconnect
      socket.onclose = null;
      socket.onerror = null;
      socket.onopen = null;
      socket.onmessage = null;
      socket.close(1000, 'Manual disconnect');
    }

    set({
      socket: null,
      isConnected: false,
      isConnecting: false,
      error: null,
      reconnectAttempts: 0,
    });
  },

  send: (data: string) => {
    const state = get();
    
    if (state.socket && state.socket.readyState === WebSocket.OPEN) {
      try {
        state.socket.send(data);
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    } else {
      console.warn('Cannot send data: WebSocket is not connected');
      return false;
    }
  },

  setConnected: (connected: boolean) => set({ isConnected: connected }),
  setConnecting: (connecting: boolean) => set({ isConnecting: connecting }),
  setError: (error: string | null) => set({ error }),
  incrementReconnectAttempts: () => set((state) => ({ reconnectAttempts: state.reconnectAttempts + 1 })),
  resetReconnectAttempts: () => set({ reconnectAttempts: 0 }),

  // Private method for scheduling reconnection
  scheduleReconnect: () => {
    const state = get();
    
    if (state.reconnectAttempts >= WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      set({ error: ERROR_MESSAGES.WEBSOCKET_CONNECTION_LOST });
      return;
    }

    const delay = Math.min(
      WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(WEBSOCKET_CONFIG.RECONNECT_DECAY, state.reconnectAttempts),
      WEBSOCKET_CONFIG.MAX_RECONNECT_INTERVAL
    );

    console.log(`Scheduling reconnect attempt ${state.reconnectAttempts + 1} in ${delay}ms`);

    const timer = window.setTimeout(() => {
      get().incrementReconnectAttempts();
      get().connect();
    }, delay);

    set({ reconnectTimer: timer });
  },
} as any));

// Selector hooks for better performance
export const useWebSocketConnection = () => useWebSocketStore((state) => ({
  isConnected: state.isConnected,
  isConnecting: state.isConnecting,
  error: state.error,
}));

export const useWebSocketActions = () => useWebSocketStore((state) => ({
  connect: state.connect,
  disconnect: state.disconnect,
  send: state.send,
}));
