import React, { useEffect, useRef } from 'react';
import { Terminal as XTerm } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';
import styled from 'styled-components';

import { useTerminalStore } from '@/stores/terminalStore';
import { useWebSocketStore } from '@/stores/websocketStore';
import { xtermService } from '@/services/xtermService';
import type { TerminalProps } from '@/types';

const TerminalContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  box-sizing: border-box;
  /* Ensure it fills the parent */
  min-height: 400px;

  .xterm {
    width: 100%;
    height: 100%;
  }

  .xterm-viewport {
    background: transparent !important;
  }

  .xterm-screen {
    background: transparent !important;
  }
`;

const Terminal: React.FC<TerminalProps> = ({ className, onReady }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstanceRef = useRef<XTerm | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const mountedRef = useRef(true);

  const config = useTerminalStore((state) => state.config);
  const setTerminal = useTerminalStore((state) => state.setTerminal);
  const setFitAddon = useTerminalStore((state) => state.setFitAddon);
  const setReady = useTerminalStore((state) => state.setReady);
  const addToBuffer = useTerminalStore((state) => state.addToBuffer);
  const { socket, isConnected } = useWebSocketStore();

  useEffect(() => {
    if (!terminalRef.current || !mountedRef.current) return;

    const container = terminalRef.current;
    if (!container) return;

    console.log('Initializing terminal...');
    console.log('Terminal container dimensions:', {
      width: container.offsetWidth,
      height: container.offsetHeight,
      clientWidth: container.clientWidth,
      clientHeight: container.clientHeight
    });

    // Check if container has valid dimensions
    if (container.offsetWidth === 0 || container.offsetHeight === 0) {
      console.warn('Terminal container has no dimensions, will proceed anyway...');
      // Don't retry, just proceed with initialization
      // The CSS should ensure the container has dimensions
    }

    console.log('Creating terminal instance...');
    const terminal = new XTerm({
      fontFamily: config.fontFamily,
      fontSize: config.fontSize,
      lineHeight: config.lineHeight,
      cursorBlink: config.cursorBlink,
      cursorStyle: config.cursorStyle,
      theme: config.theme,
      allowProposedApi: true,
    });
    console.log('Creating addons...');
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    console.log('Opening terminal in container...');
    terminal.open(container);
    console.log('Fitting terminal...');
    setTimeout(() => {
      if (mountedRef.current) {
        try {
          fitAddon.fit();
          console.log('Terminal fitted successfully');
        } catch (error) {
          console.warn('Failed to fit terminal:', error);
        }
      }
    }, 100);

    console.log('Setting up terminal references...');
    terminalInstanceRef.current = terminal;
    fitAddonRef.current = fitAddon;
    setTerminal(terminal);
    setFitAddon(fitAddon);
    setReady(true);
    onReady?.();

    console.log('[Terminal] Terminal is ready');
    terminal.onData((data) => {
      console.log('[Terminal] Terminal input:', data);
      addToBuffer(data);

      // Send data to WebSocket if connected
      if (socket && isConnected) {
        socket.send(JSON.stringify({
          type: 'input',
          data: data
        }));
      } else {
        // Fallback: echo locally if not connected
        terminal.write(data);
      }
    });
    terminal.onResize(({ cols, rows }) => {
      console.log('[Terminal] Terminal resized:', { cols, rows });
      if (socket && isConnected) {
        socket.send(JSON.stringify({
          type: 'resize',
          data: JSON.stringify({ cols, rows })
        }));
      }
    });
    terminal.writeln('Welcome to Web Terminal!');
    terminal.write('$ ');
    const handleResize = () => {
      window.setTimeout(() => {
        if (fitAddon && mountedRef.current) {
          try {
            fitAddon.fit();
          } catch {}
        }
      }, 0);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      // console.log('[Terminal] [1] Terminal unmounting...');
      // mountedRef.current = false;
      // window.removeEventListener('resize', handleResize);
      // setReady(false);
      // try {
      //   terminal.dispose();
      // } catch {}
    };
  }, []); // 依赖项为空数组，只在挂载时执行

  useEffect(() => {
    const terminal = terminalInstanceRef.current;
    if (!terminal || !mountedRef.current) return;

    try {
      terminal.options.fontFamily = config.fontFamily;
      terminal.options.fontSize = config.fontSize;
      terminal.options.lineHeight = config.lineHeight;
      terminal.options.cursorBlink = config.cursorBlink;
      terminal.options.cursorStyle = config.cursorStyle;
      terminal.options.theme = config.theme;

      // Refresh terminal display
      terminal.refresh(0, terminal.rows - 1);

      // Refit terminal
      if (fitAddonRef.current && mountedRef.current) {
        window.setTimeout(() => {
          if (mountedRef.current && fitAddonRef.current) {
            try {
              fitAddonRef.current.fit();
            } catch (error) {
              console.warn('Failed to fit terminal after config update:', error);
            }
          }
        }, 0);
      }
    } catch (error) {
      console.warn('Failed to update terminal options:', error);
    }
  }, [config]);

  // Handle WebSocket connection changes
  useEffect(() => {
    const terminal = terminalInstanceRef.current;

    if (!mountedRef.current) return;

    if (socket && isConnected && terminal) {
      console.log('WebSocket connected, initializing XTerm service...');
      // Initialize XTerm service
      xtermService.initialize(terminal, socket);
      if (mountedRef.current) {
        setReady(true);
        onReady?.();
      }

      try {
        terminal.writeln('\r\nConnected to terminal server');
        terminal.write('$ ');
      } catch (error) {
        console.warn('Failed to write to terminal:', error);
      }
    } else if (!isConnected && terminal) {
      console.log('WebSocket disconnected');
      if (mountedRef.current) {
        setReady(false);
      }
      try {
        terminal.writeln('\r\nDisconnected from terminal server');
        terminal.write('$ ');
      } catch (error) {
        console.warn('Failed to write to terminal:', error);
      }
    }
  }, [socket, isConnected, onReady, setReady]);

  // Handle WebSocket messages
  useEffect(() => {
    if (!socket || !mountedRef.current) return;

    const handleMessage = (event: MessageEvent) => {
      if (!mountedRef.current) return;

      try {
        const message = JSON.parse(event.data);
        const terminal = terminalInstanceRef.current;

        if (terminal && message.type === 'output') {
          terminal.write(message.data);
        }
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    socket.addEventListener('message', handleMessage);

    return () => {
      socket.removeEventListener('message', handleMessage);
    };
  }, [socket]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return (
    <TerminalContainer className={className}>
      <div ref={terminalRef} />
    </TerminalContainer>
  );
};

export default Terminal;
