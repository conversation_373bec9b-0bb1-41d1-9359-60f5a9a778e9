import React from 'react';
import { Space, Typography } from 'antd';
import { ClockCircleOutlined } from '@ant-design/icons';
import styled from 'styled-components';

// import type { StatusBarProps, SessionInfo } from '@/types';
// import { SESSION_CONFIG, API_ENDPOINTS } from '@/constants';

const { Text } = Typography;

const StatusContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 24px;
`;

const StatusText = styled(Text)`
  color: #52c41a !important;
  font-weight: 500;
`;

const StatusBar: React.FC = () => {
  // Simplified for debugging - just show connection status

  return (
    <StatusContainer>
      <Space align="center">
        <ClockCircleOutlined style={{ color: '#f0f0f0' }} />
        <StatusText>
          Terminal Ready
        </StatusText>
      </Space>
    </StatusContainer>
  );
};

export default StatusBar;
