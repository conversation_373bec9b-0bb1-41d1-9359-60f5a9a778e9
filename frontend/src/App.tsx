import { useEffect } from 'react';
import { ConfigProvider, theme } from 'antd';

import Layout from '@/components/Layout/Layout';
import Terminal from '@/components/Terminal/Terminal';
// import VirtualKeyboard from '@/components/VirtualKeyboard/VirtualKeyboard';
// import AIDialog from '@/components/AIDialog/AIDialog';
// import ContentModal from '@/components/ContentModal/ContentModal';
// import SettingsModal from '@/components/SettingsModal/SettingsModal';
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary';
import { useWebSocketStore } from '@/stores/websocketStore';
// import { useKeyboardActions } from '@/stores/keyboardStore';
// import { useAIActions } from '@/stores/aiStore';
// import { xtermService } from '@/services/xtermService';

function App() {
  const { connect, disconnect } = useWebSocketStore();

  useEffect(() => {
    console.log('App mounting, connecting to WebSocket...');
    // Connect to WebSocket when app starts
    connect();

    // Cleanup on unmount
    return () => {
      console.log('App unmounting, disconnecting...');
      disconnect();
    };
  }, [connect, disconnect]);

  const handleTerminalReady = () => {
    console.log('Terminal is ready');
  };

  // Simplified placeholder handlers for Layout component
  const handleGetAllContent = () => {
    console.log('Get all content - disabled for debugging');
  };

  const handleToggleKeyboard = () => {
    console.log('Toggle keyboard - disabled for debugging');
  };

  const handleOpenAI = () => {
    console.log('Open AI dialog - disabled for debugging');
  };

  const handleOpenSettings = () => {
    console.log('Open settings - disabled for debugging');
  };

  return (
    <ErrorBoundary>
      <ConfigProvider
        theme={{
          algorithm: theme.darkAlgorithm,
          token: {
            colorPrimary: '#52c41a',
            colorBgBase: '#1e1e1e',
            colorTextBase: '#f0f0f0',
          },
        }}
      >
        <Layout
          onGetAllContent={handleGetAllContent}
          onToggleKeyboard={handleToggleKeyboard}
          onOpenAI={handleOpenAI}
          onOpenSettings={handleOpenSettings}
        >
          <Terminal onReady={handleTerminalReady} />
          {/* Commented out for debugging */}
          {/* <VirtualKeyboard /> */}
          {/* <ContentModal
            open={contentModalOpen}
            content={terminalContent}
            onClose={() => setContentModalOpen(false)}
          /> */}
          {/* <AIDialog /> */}
          {/* <SettingsModal
            open={settingsModalOpen}
            onClose={() => setSettingsModalOpen(false)}
          /> */}
        </Layout>
      </ConfigProvider>
    </ErrorBoundary>
  );
}

export default App;
