const WebSocket = require('ws');
const http = require('http');

// Create HTTP server
const server = http.createServer();

// Create WebSocket server
const wss = new WebSocket.Server({ 
  server,
  path: '/ws'
});

console.log('Starting WebSocket test server...');

wss.on('connection', function connection(ws, req) {
  console.log('New WebSocket connection from:', req.socket.remoteAddress);
  
  // Send welcome message
  ws.send(JSON.stringify({
    type: 'output',
    data: 'Welcome to Web Terminal!\r\n$ '
  }));

  ws.on('message', function incoming(message) {
    try {
      const data = JSON.parse(message);
      console.log('Received:', data);
      
      if (data.type === 'input') {
        // Echo the input back
        ws.send(JSON.stringify({
          type: 'output',
          data: data.data
        }));
        
        // If it's Enter, send a new prompt
        if (data.data === '\r') {
          ws.send(JSON.stringify({
            type: 'output',
            data: '\r\n$ '
          }));
        }
      } else if (data.type === 'resize') {
        console.log('Terminal resized:', data.data);
      }
    } catch (error) {
      console.error('Failed to parse message:', error);
    }
  });

  ws.on('close', function close() {
    console.log('WebSocket connection closed');
  });

  ws.on('error', function error(err) {
    console.error('WebSocket error:', err);
  });
});

// Handle server errors
server.on('error', (err) => {
  console.error('Server error:', err);
});

// Start server
const PORT = 8082;
server.listen(PORT, () => {
  console.log(`WebSocket test server running on http://localhost:${PORT}`);
  console.log(`WebSocket endpoint: ws://localhost:${PORT}/ws`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down server...');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
